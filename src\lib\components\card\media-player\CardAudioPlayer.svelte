<script lang="ts">
	import 'media-chrome';

	let {
		url,
		preload = 'metadata'
	}: { url: string; preload?: '' | 'none' | 'metadata' | 'auto' | null | undefined } = $props();
</script>

<media-controller audio class="w-full">
	<audio slot="media" src={url} {preload}></audio>
	<media-control-bar class="flex">
		<media-play-button></media-play-button>
		<div class="grow"></div>
		<media-time-display showduration></media-time-display>
		<div class="grow"></div>
		<media-mute-button></media-mute-button>
	</media-control-bar>
</media-controller>
