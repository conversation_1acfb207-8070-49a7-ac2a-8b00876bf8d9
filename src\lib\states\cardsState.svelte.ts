import type { Card } from '$lib/models/card';

export class CardsState {
	cards: Card[];

	constructor(cards?: Card[]) {
		this.cards = $state(cards ?? []);
	}

	get topCard(): Card | undefined {
		return this.cards[0];
	}

	get bottomCard(): Card | undefined {
		return this.cards[this.cards.length - 1];
	}

	addOnTop(card: Card) {
		this.cards = [card, ...this.cards];
	}

	addAtBottom(card: Card) {
		this.cards.push(card);
	}

	remove(card: Card) {
		this.removeById(card.id);
	}

	removeById(id: number) {
		this.cards = this.cards.filter((c) => c.id !== id);
	}

	removeFromTop() {
		return this.cards.shift();
	}

	removeFromBottom() {
		return this.cards.pop();
	}
}
