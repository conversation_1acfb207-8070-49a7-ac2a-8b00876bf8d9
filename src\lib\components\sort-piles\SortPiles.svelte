<script lang="ts">
	import type { CardConfiguration } from '$lib/models/cardConfiguration';
	import type { SortPilesConfiguration } from '$lib/models/sortPilesConfiguration';
	import type { Card as CardModel } from '$lib/models/card';
	import SortPile from './SortPile.svelte';

	interface SortPilesProps {
		pileNameToCards: Map<string, CardModel[]>;
		sortPilesConfiguration: SortPilesConfiguration;
		cardConfiguration: CardConfiguration;
		onDroppedToPile?: (card: CardModel, pileName: string) => void;
	}

	let {
		pileNameToCards,
		sortPilesConfiguration,
		cardConfiguration,
		onDroppedToPile
	}: SortPilesProps = $props();
</script>

<ul class="flex gap-8">
	{#each sortPilesConfiguration.pileNames as pileName (pileName)}
		<li class="flex flex-col items-center">
			<SortPile
				{pileName}
				cards={pileNameToCards.get(pileName)!}
				{cardConfiguration}
				onDroppedTo={onDroppedToPile}
			/>
		</li>
	{/each}
</ul>
