import { getContext, setContext } from 'svelte';
import { CardsState } from '$lib/states/cardsState.svelte';
import type { Card } from '$lib/models/card';

export class RevealedCardsState extends CardsState {}

const REVEALED_CARDS_KEY = Symbol('REVEALED_CARDS');

export function setRevealedCardsState(cards?: Card[]) {
	return setContext(REVEALED_CARDS_KEY, new RevealedCardsState(cards));
}

export function getRevealedCardsState() {
	return getContext<ReturnType<typeof setRevealedCardsState>>(REVEALED_CARDS_KEY);
}
