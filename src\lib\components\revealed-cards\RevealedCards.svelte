<script lang="ts">
	import Card from '$lib/components/card/Card.svelte';
	import CardContainer from '$lib/components/card/CardContainer.svelte';
	import { flip } from 'svelte/animate';
	import type { Card as CardModel } from '$lib/models/card';
	import type { CardConfiguration } from '$lib/models/cardConfiguration';
	import type { RevealedCardsConfiguration } from '$lib/models/revealedCardsConfiguration';
	import { receive, send } from '$lib/transitions/crossfadeCard';
	import { sineIn } from 'svelte/easing';
	import { fly } from 'svelte/transition';
	import { dragndrop } from '$lib/attachments/dragndrop.svelte';

	interface RevealedCardsProps {
		cards: CardModel[];
		revealedCardsConfiguration: RevealedCardsConfiguration;
		cardConfiguration: CardConfiguration;
	}

	let { cards, revealedCardsConfiguration, cardConfiguration }: RevealedCardsProps = $props();
	let visibleCards = $derived(cards?.slice(0, revealedCardsConfiguration.numRevealSpots));
</script>

<div class="relative">
	<!-- Reveal Spots -->
	<ul class="flex gap-8">
		{#each Array(revealedCardsConfiguration.numRevealSpots) as _, i (i)}
			<CardContainer
				widthPx={cardConfiguration.widthPx}
				aspectRatio={cardConfiguration.aspectRatio}
			>
				<div
					class="h-full w-full {cardConfiguration.cornerRadius
						? `rounded-${cardConfiguration.cornerRadius}`
						: ''} border-8 border-zinc-200"
				></div>
			</CardContainer>
		{/each}
	</ul>

	<!-- Revealed Cards -->
	<div class="absolute inset-0">
		<div
			class="grid grid-rows-1 gap-8"
			style="grid-template-columns: repeat(
				{revealedCardsConfiguration.numRevealSpots},
				{cardConfiguration.widthPx}px
			);"
		>
			{#each visibleCards as card, i (card.id)}
				<div
					class="col-span-1 row-start-1"
					style="grid-column-start: {i + 1 <= revealedCardsConfiguration.numRevealSpots
						? i + 1
						: revealedCardsConfiguration.numRevealSpots};"
					in:receive={{ key: card.id, duration: 600, easing: sineIn }}
					out:fly={{ duration: 600, x: 300, easing: sineIn }}
					animate:flip={{ duration: 600, easing: sineIn }}
				>
					<div
						{@attach dragndrop({ targets: '.card-drop-target', data: card, isEnabled: true })}
						out:send={{ key: card.id, duration: 600, easing: sineIn }}
					>
						<Card {...card} {cardConfiguration} />
					</div>
				</div>
			{/each}
		</div>
	</div>
</div>
