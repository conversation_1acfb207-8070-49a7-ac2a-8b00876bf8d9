<script lang="ts">
	import type { Snippet } from 'svelte';
	import type { HTMLAttributes } from 'svelte/elements';

	interface CardContainerProps extends HTMLAttributes<HTMLDivElement> {
		widthPx: number;
		aspectRatio: number;
		children: Snippet;
	}

	let { widthPx, aspectRatio, children, ...rest }: CardContainerProps = $props();
</script>

<div {...rest} style="width: {widthPx}px; aspect-ratio: {aspectRatio}">
	{@render children()}
</div>
