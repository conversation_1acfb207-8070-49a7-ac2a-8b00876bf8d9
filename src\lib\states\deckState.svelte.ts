import { getContext, setContext } from 'svelte';
import { CardsState } from '$lib/states/cardsState.svelte';
import type { Card } from '$lib/models/card';

export class DeckState extends CardsState {}

const DECK_KEY = Symbol('DECK');

export function setDeckState(cards?: Card[]) {
	return setContext(DECK_KEY, new DeckState(cards));
}

export function getDeckState() {
	return getContext<ReturnType<typeof setDeckState>>(DECK_KEY);
}
