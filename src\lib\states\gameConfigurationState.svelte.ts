import type { CardConfiguration } from '$lib/models/cardConfiguration';
import type { DeckConfiguration } from '$lib/models/deckConfiguration';
import type { GameConfiguration } from '$lib/models/gameConfiguration';
import type { RevealedCardsConfiguration } from '$lib/models/revealedCardsConfiguration';
import type { SortPilesConfiguration } from '$lib/models/sortPilesConfiguration';
import { getContext, setContext } from 'svelte';

export class GameConfigurationState {
	cardConfiguration: CardConfiguration;
	revealedCardsConfiguration: RevealedCardsConfiguration;
	deckConfiguration: DeckConfiguration;
	sortPilesConfiguration: SortPilesConfiguration;

	constructor(gameConfiguration?: GameConfiguration) {
		this.cardConfiguration = $state(
			Object.assign({}, this.getDefaultCardConfiguration(), gameConfiguration?.cardConfiguration)
		);
		this.revealedCardsConfiguration = $state(
			Object.assign(
				{},
				this.getDefaultRevealedCardsConfiguration(),
				gameConfiguration?.revealedCardsConfiguration
			)
		);
		this.deckConfiguration = $state(
			Object.assign({}, this.getDefaultDeckConfiguration(), gameConfiguration?.deckConfiguration)
		);
		this.sortPilesConfiguration = $state(
			Object.assign(
				{},
				this.getDefaultSortPilesConfiguration(),
				gameConfiguration?.sortPilesConfiguration
			)
		);
	}

	protected getDefaultCardConfiguration(): CardConfiguration {
		return {
			widthPx: 180,
			aspectRatio: 1 / 1.4,
			cornerRadius: 'xl'
		};
	}

	protected getDefaultRevealedCardsConfiguration(): RevealedCardsConfiguration {
		return {
			numRevealSpots: 1
		};
	}

	protected getDefaultDeckConfiguration(): DeckConfiguration {
		return {
			numVisibleCards: 4,
			initialCardAngleDeg: -6,
			cardAngleDegStep: 6
		};
	}

	protected getDefaultSortPilesConfiguration(): SortPilesConfiguration {
		return {
			pileNames: []
		};
	}
}

const GAVE_CONFIGURATION_KEY = Symbol('GAME_CONFIGURATION');

export function setGameConfigurationState(gameConfiguration?: GameConfiguration) {
	return setContext(GAVE_CONFIGURATION_KEY, new GameConfigurationState(gameConfiguration));
}

export function getGameConfigurationState() {
	return getContext<ReturnType<typeof setGameConfigurationState>>(GAVE_CONFIGURATION_KEY);
}
