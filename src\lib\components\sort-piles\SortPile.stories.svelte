<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import SortPile from '$lib/components/sort-piles/SortPile.svelte';

	const { Story } = defineMeta({
		title: 'Components/SortPiles/SortPile',
		component: SortPile,
		tags: ['autodocs'],
		argTypes: {
			pileName: { control: 'text' },
			cards: { control: 'object' },
			cardConfiguration: { control: 'object' }
		}
	});
</script>

<script lang="ts">
	import { setGameConfigurationState } from '$lib/states/gameConfigurationState.svelte';

	let gameConfigurationState = setGameConfigurationState();
</script>

<Story
	name="Primary"
	args={{
		pileName: 'Pile 1',
		cards: [],
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>

<Story
	name="WithCard"
	args={{
		pileName: 'Pile 1',
		cards: [
			{
				id: 1,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 1' },
							body: { text: 'Body 1' },
							footer: { text: 'Footer 1' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } },
				isFrontVisible: true
			}
		],
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
/>

<Story
	name="WithDropHighlight"
	args={{
		pileName: 'Pile 1',
		cards: [],
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
	play={async ({ canvasElement }) => {
		const sortPileEl = canvasElement.querySelector('.sort-pile');
		sortPileEl!.classList.add('drop-target');
	}}
/>

<Story
	name="WithDropHighlightOverCard"
	args={{
		pileName: 'Pile 1',
		cards: [
			{
				id: 1,
				frontSide: {
					content: {
						multiText: {
							title: { text: 'Title 1' },
							body: { text: 'Body 1' },
							footer: { text: 'Footer 1' }
						}
					}
				},
				backSide: { content: { image: { url: 'https://picsum.photos/180/252' } } },
				isFrontVisible: true
			}
		],
		cardConfiguration: gameConfigurationState.cardConfiguration
	}}
	play={async ({ canvasElement }) => {
		const sortPileEl = canvasElement.querySelector('.sort-pile');
		sortPileEl!.classList.add('drop-target');
	}}
/>
