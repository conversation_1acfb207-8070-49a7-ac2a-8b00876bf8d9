<script lang="ts">
	import type { CardConfiguration } from '$lib/models/cardConfiguration';
	import Card from '$lib/components/card/Card.svelte';
	import CardContainer from '$lib/components/card/CardContainer.svelte';
	import type { Card as CardModel } from '$lib/models/card';
	import { receive } from '$lib/transitions/crossfadeCard';
	import { sineIn } from 'svelte/easing';

	interface SortPileProps {
		pileName: string;
		cards: CardModel[];
		cardConfiguration: CardConfiguration;
		onDroppedTo?: (card: CardModel, pileName: string) => void;
	}

	let { pileName, cards, cardConfiguration, onDroppedTo }: SortPileProps = $props();

	let topCard = $derived(cards[0]);
</script>

<div class="sort-pile w-min">
	<CardContainer class="relative" {...cardConfiguration}>
		{#if topCard}
			<div in:receive={{ key: topCard.id, duration: 600, easing: sineIn }}>
				<Card {...topCard} {cardConfiguration} />
			</div>
		{:else}
			<div
				class="h-full w-full rounded-lg border-8 border-zinc-200 {cardConfiguration.cornerRadius
					? `rounded-${cardConfiguration.cornerRadius}`
					: ''}"
			></div>
		{/if}
		<div
			class="card-drop-target absolute inset-0 bg-blue-500 opacity-0 {cardConfiguration.cornerRadius
				? `rounded-${cardConfiguration.cornerRadius}`
				: ''}"
			ondroppedto={(event: CustomEvent<CardModel>) => {
				onDroppedTo?.(event.detail, pileName);
			}}
		></div>
	</CardContainer>
	<p class="text-center text-lg font-medium">{pileName}</p>
</div>

<style>
	.card-drop-target.drop-target {
		opacity: 0.3;
	}
</style>
