<script lang="ts">
	import 'media-chrome';

	let {
		url,
		aspectRatio = 1 / 1.4,
		preload = 'metadata'
	}: {
		url: string;
		aspectRatio?: number;
		preload?: '' | 'none' | 'metadata' | 'auto' | null | undefined;
	} = $props();
</script>

<media-controller>
	<video
		slot="media"
		src={url}
		style="aspect-ratio: {aspectRatio}"
		class="h-full w-full"
		{preload}
		playsinline><track kind="captions" /></video
	>
	<media-play-button slot="centered-chrome"></media-play-button>
	<media-control-bar>
		<media-play-button></media-play-button>
		<media-mute-button></media-mute-button>
		<div class="grow"></div>
		<media-fullscreen-button></media-fullscreen-button>
	</media-control-bar>
</media-controller>
