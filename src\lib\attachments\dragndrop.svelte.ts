import type { Attachment } from 'svelte/attachments';
import { quadOut } from 'svelte/easing';
import { Tween } from 'svelte/motion';

interface ScrollOffset {
	scrollTop: number;
	scrollLeft: number;
}

export interface DragDropParams {
	isEnabled: boolean;
	targets: string;
	data: object;
}

const DropTargetClass = 'drop-target';
const DragThresholdPixels = 5;

// let zoomFactorValue = 1.0;

// TODO
// zoomFactor.subscribe((value) => {
// 	zoomFactorValue = value;
// });

export function dragndrop(params: DragDropParams): Attachment {
	return (el: Element) => {
		const node = el as HTMLElement;
		let x: number;
		let y: number;
		let offsetX: number = 0;
		let offsetY: number = 0;
		let elementScrollOffsetMap: WeakMap<HTMLElement, ScrollOffset>;
		let originalZIndex: string;
		let originalPointerEvents: string;
		let dropTarget: Element | undefined;
		let lastTouchEvent: TouchEvent | undefined = undefined;
		let scaleFactorValue = 1.0;
		let isDragging: boolean = false;

		const tweenedOffset = new Tween(
			{ x: 0, y: 0 },
			{
				duration: 200,
				easing: quadOut
			}
		);

		$effect(() => {
			setNodeTransform(tweenedOffset.current.x, tweenedOffset.current.y);
		});

		function setNodeTransform(x: number, y: number) {
			node.style.transform = `translate(${x}px, ${y}px)`;
		}

		function onMouseDown(event: MouseEvent) {
			const mouseClientX = event.clientX;
			const mouseClientY = event.clientY;

			onDragStart(mouseClientX, mouseClientY, addMouseEvents);
		}

		function onTouchStart(event: TouchEvent) {
			const touch = event.touches[0];
			const touchClientX = touch.clientX;
			const touchClientY = touch.clientY;
			lastTouchEvent = event;

			onDragStart(touchClientX, touchClientY, addTouchEvents);
		}

		function addMouseEvents() {
			window.addEventListener('mousemove', onMouseMove);
			window.addEventListener('mouseup', onMouseUp);
			window.addEventListener('scroll', onScroll, true);
		}

		function addTouchEvents() {
			window.addEventListener('touchmove', onTouchMove);
			window.addEventListener('touchend', onTouchEnd);
			window.addEventListener('scroll', onScroll, true);
		}

		function onDragStart(clientX: number, clientY: number, addEventsFn: () => void) {
			if (!params.isEnabled) {
				return;
			}

			x = clientX;
			y = clientY;
			offsetX = 0;
			offsetY = 0;

			const rect = node.getBoundingClientRect();
			scaleFactorValue = rect.width / node.offsetWidth;

			// node.dispatchEvent(
			//   new CustomEvent("dragstart", {
			//     detail: { x, y },
			//   })
			// );

			elementScrollOffsetMap = new WeakMap<HTMLElement, ScrollOffset>();

			let currentElement = node;
			while (currentElement.parentElement) {
				currentElement = currentElement.parentElement;
				elementScrollOffsetMap.set(currentElement, {
					scrollTop: currentElement.scrollTop,
					scrollLeft: currentElement.scrollLeft
				});
			}

			originalZIndex = node.style.zIndex;
			node.style.zIndex = String(Number.MAX_SAFE_INTEGER);

			addEventsFn();
		}

		function onMouseMove(event: MouseEvent) {
			const mouseX = event.clientX;
			const mouseY = event.clientY;

			onMove(mouseX, mouseY);
		}

		function onTouchMove(event: TouchEvent) {
			const touch = event.touches[0];
			const touchX = touch.clientX;
			const touchY = touch.clientY;
			lastTouchEvent = event;

			onMove(touchX, touchY);
		}

		function onMove(newX: number, newY: number) {
			const dX = newX - x;
			const dY = newY - y;
			const scaledDeltaX = dX / scaleFactorValue; // / zoomFactorValue;
			const scaledDeltaY = dY / scaleFactorValue; // / zoomFactorValue;
			offsetX += scaledDeltaX;
			offsetY += scaledDeltaY;
			x = newX;
			y = newY;

			if (!isDragging) {
				const maxAbsoluteOffset = Math.max(Math.abs(offsetX), Math.abs(offsetY));
				if (maxAbsoluteOffset <= DragThresholdPixels) {
					return;
				}
			}

			if (!isDragging) {
				// Needed for when drop target (mouse pointer) is obscured by the card itself
				originalPointerEvents = node.style.pointerEvents;
				node.style.pointerEvents = 'none';

				isDragging = true;
			}

			const dropTargetCandidate = document.elementFromPoint(x, y);
			if (dropTargetCandidate) {
				if (dropTargetCandidate !== dropTarget) {
					if (dropTarget) {
						dropTarget.classList.remove(DropTargetClass);
						dropTarget = undefined;
					}

					if (dropTargetCandidate.matches(params.targets)) {
						dropTarget = dropTargetCandidate;
						dropTarget.classList.add(DropTargetClass);
					}
				}
			} else {
				if (dropTarget) {
					dropTarget.classList.remove(DropTargetClass);
					dropTarget = undefined;
				}
			}

			setNodeTransform(offsetX, offsetY);

			// node.dispatchEvent(
			//   new CustomEvent("drag", {
			//     detail: { x, y, dX, dY, offsetX, offsetY },
			//   })
			// );
		}

		function onMouseUp(event: MouseEvent) {
			const mouseClientX = event.clientX;
			const mouseClientY = event.clientY;

			onDragEnd(mouseClientX, mouseClientY, event, removeMouseEvents);
		}

		function onTouchEnd(event: TouchEvent) {
			if (lastTouchEvent === undefined) {
				console.error('Unexpected: last touch event should be set');
				return;
			}

			const touch = lastTouchEvent.touches[0];
			const touchClientX = touch.clientX;
			const touchClientY = touch.clientY;

			onDragEnd(touchClientX, touchClientY, event, removeTouchEvents);
		}

		function removeMouseEvents() {
			window.removeEventListener('mousemove', onMouseMove);
			window.removeEventListener('mouseup', onMouseUp);
			window.removeEventListener('scroll', onScroll, true);
		}

		function removeTouchEvents() {
			window.removeEventListener('touchmove', onTouchMove);
			window.removeEventListener('touchend', onTouchEnd);
			window.removeEventListener('scroll', onScroll, true);
		}

		function onDragEnd(
			clientX: number,
			clientY: number,
			event: MouseEvent | TouchEvent,
			removeEventsFn: () => void
		) {
			x = clientX;
			y = clientY;

			// node.dispatchEvent(
			//   new CustomEvent("dragend", {
			//     detail: { x, y },
			//   })
			// );

			if (dropTarget) {
				node.dispatchEvent(
					new CustomEvent('droppedfrom', {
						detail: params.data
					})
				);

				dropTarget.dispatchEvent(
					new CustomEvent('droppedto', {
						detail: params.data
					})
				);

				dropTarget.classList.remove(DropTargetClass);
				dropTarget = undefined;
			} else {
				tweenedOffset.set(
					{ x: offsetX, y: offsetY },
					{
						duration: 0,
						easing: quadOut
					}
				);

				tweenedOffset.set(
					{ x: 0, y: 0 },
					{
						duration: 250,
						easing: quadOut
					}
				);
			}

			if (isDragging) {
				node.style.zIndex = originalZIndex;
				node.style.pointerEvents = originalPointerEvents;

				event.stopPropagation();
				event.preventDefault();
			}

			removeEventsFn();

			isDragging = false;
		}

		function onScroll(event: Event) {
			const targetElement = <HTMLElement>event.target;
			const scrollOffset = elementScrollOffsetMap.get(targetElement);
			if (scrollOffset === undefined) {
				return;
			}

			const scrollTop = targetElement.scrollTop;
			const scrollLeft = targetElement.scrollLeft;
			const dScrollTop = scrollTop - scrollOffset.scrollTop;
			const dScrollLeft = scrollLeft - scrollOffset.scrollLeft;
			offsetY += dScrollTop;
			offsetX += dScrollLeft;

			elementScrollOffsetMap.set(targetElement, {
				scrollTop,
				scrollLeft
			});

			setNodeTransform(offsetX, offsetY);
		}

		node.addEventListener('mousedown', onMouseDown);
		node.addEventListener('touchstart', onTouchStart);

		return () => {
			node.removeEventListener('mousedown', onMouseDown);
			node.removeEventListener('touchstart', onTouchStart);
		};
	};
}
