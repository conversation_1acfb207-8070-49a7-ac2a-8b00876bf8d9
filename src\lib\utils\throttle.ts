// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function throttle<T extends (...args: any[]) => any>(func: T, delay: number): T {
	let lastCallTime = 0;
	let timeoutId: ReturnType<typeof setTimeout> | null = null;

	return function (...args: Parameters<T>) {
		const now = Date.now();
		const timeSinceLastCall = now - lastCallTime;

		if (timeSinceLastCall >= delay) {
			lastCallTime = now;
			func(...args);
		} else if (!timeoutId) {
			const remainingTime = delay - timeSinceLastCall;
			timeoutId = setTimeout(() => {
				lastCallTime = Date.now();
				timeoutId = null;
				func(...args);
			}, remainingTime);
		}
	} as T;
}
