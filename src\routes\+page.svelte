<script>
	import Card from '$lib/components/card/Card.svelte';
	import { setGameConfigurationState } from '$lib/states/gameConfigurationState.svelte';

	let isFrontVisible = $state(false);
	let gameConfigurationState = setGameConfigurationState();
</script>

<div class="flex flex-col items-center justify-between p-16">
	<Card
		frontSide={{
			content: {
				multiText: { title: { text: 'Title' }, body: { text: 'Body' }, footer: { text: 'Footer' } }
			}
		}}
		backSide={{ backgroundImageUrl: 'https://picsum.photos/180/252' }}
		{isFrontVisible}
		cardConfiguration={gameConfigurationState.cardConfiguration}
	></Card>

	<button
		class="mt-4 rounded-lg bg-gray-200 px-4 py-2"
		onclick={() => (isFrontVisible = !isFrontVisible)}>Flip</button
	>
</div>
