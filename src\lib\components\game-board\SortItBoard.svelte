<script lang="ts">
	import type { Card } from '$lib/models/card';
	import { setDeckState } from '$lib/states/deckState.svelte';
	import { setGameConfigurationState } from '$lib/states/gameConfigurationState.svelte';
	import { setRevealedCardsState } from '$lib/states/revealedCardsState.svelte';
	import Deck from '$lib/components/deck/Deck.svelte';
	import RevealedCards from '$lib/components/revealed-cards/RevealedCards.svelte';
	import { tick } from 'svelte';
	import { throttle } from '$lib/utils/throttle';
	import type { GameConfiguration } from '$lib/models/gameConfiguration';
	import SortPiles from '../sort-piles/SortPiles.svelte';
	import { setSortPilesState } from '$lib/states/sortPilesState.svelte';

	interface SortItBoardProps {
		cards: Card[];
		gameConfiguration?: GameConfiguration;
	}

	let { cards, gameConfiguration }: SortItBoardProps = $props();

	let gameConfigurationState = setGameConfigurationState(gameConfiguration);
	let deckState = setDeckState(cards);
	let revealedCardsState = setRevealedCardsState();
	let sortPilesState = setSortPilesState(gameConfigurationState.sortPilesConfiguration);

	let pileNameToCards = $derived(
		new Map(
			sortPilesState.pileNameToCards
				.keys()
				.map((pileName) => [pileName, sortPilesState.pileNameToCards.get(pileName)!.cards])
		)
	);

	async function handleClickDeck() {
		const topCard = deckState.topCard;
		if (!topCard) {
			return;
		}

		// Clone the card so that we can set isFrontVisible twice
		// allowing for the crossface transition to play the card
		// flipping animation correctly
		const topCardCopy = $state({ ...topCard });
		topCard.isFrontVisible = true;
		// Tick needed to trigger the flip animation on the outro deck card
		await tick();

		deckState.remove(topCard);
		revealedCardsState.addOnTop(topCardCopy);

		// Tick needed to trigger the flip animation on the intro revealed card
		await tick();
		topCardCopy.isFrontVisible = true;

		// Move the bottom card back to the deck if we have more cards than reveal spots
		if (
			revealedCardsState.cards.length >
			gameConfigurationState.revealedCardsConfiguration.numRevealSpots
		) {
			const cardToMoveBackToDeck = revealedCardsState.removeFromBottom();
			cardToMoveBackToDeck!.isFrontVisible = false;
			deckState.addAtBottom(cardToMoveBackToDeck!);
		}
	}

	function handleDroppedToPile(card: Card, pileName: string) {
		revealedCardsState.remove(card);
		sortPilesState.pileNameToCards.get(pileName)!.addOnTop(card);
	}
</script>

<div class="flex flex-col gap-48">
	<div class="z-10 flex gap-96">
		<Deck
			cards={deckState.cards}
			deckConfiguration={gameConfigurationState.deckConfiguration}
			cardConfiguration={gameConfigurationState.cardConfiguration}
			onClickDeck={throttle(handleClickDeck, 700)}
		/>

		<RevealedCards
			cards={revealedCardsState.cards}
			revealedCardsConfiguration={gameConfigurationState.revealedCardsConfiguration}
			cardConfiguration={gameConfigurationState.cardConfiguration}
		/>
	</div>
	<div>
		<SortPiles
			{pileNameToCards}
			sortPilesConfiguration={gameConfigurationState.sortPilesConfiguration}
			cardConfiguration={gameConfigurationState.cardConfiguration}
			onDroppedToPile={handleDroppedToPile}
		/>
	</div>
</div>
