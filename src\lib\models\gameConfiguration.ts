import type { CardConfiguration } from './cardConfiguration';
import type { DeckConfiguration } from './deckConfiguration';
import type { RevealedCardsConfiguration } from './revealedCardsConfiguration';
import type { SortPilesConfiguration } from './sortPilesConfiguration';

export interface GameConfiguration {
	cardConfiguration?: Partial<CardConfiguration>;
	revealedCardsConfiguration?: Partial<RevealedCardsConfiguration>;
	deckConfiguration?: Partial<DeckConfiguration>;
	sortPilesConfiguration?: SortPilesConfiguration;
}
