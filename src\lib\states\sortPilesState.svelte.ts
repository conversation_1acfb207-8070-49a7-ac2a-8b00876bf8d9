import { getContext, setContext } from 'svelte';
import { CardsState } from './cardsState.svelte';
import type { SortPilesConfiguration } from '$lib/models/sortPilesConfiguration';

export class SortPilesState {
	pileNameToCards: Map<string, CardsState>;

	constructor(pileNameToCards: Map<string, CardsState>) {
		this.pileNameToCards = pileNameToCards;
	}

	public static fromSortPilesConfiguration(sortPilesConfiguration: SortPilesConfiguration) {
		const pileNameToCards = new Map<string, CardsState>();
		for (const pileName of sortPilesConfiguration.pileNames) {
			pileNameToCards.set(pileName, new CardsState());
		}

		return new SortPilesState(pileNameToCards);
	}
}

const SORT_PILES_KEY = Symbol('SORT_PILES');

export function setSortPilesState(sortPilesConfiguration: SortPilesConfiguration): SortPilesState;
export function setSortPilesState(pileNameToCards: Map<string, CardsState>): SortPilesState;
export function setSortPilesState(
	sortPilesConfigurationOrPileNameToCards: SortPilesConfiguration | Map<string, CardsState>
) {
	if (sortPilesConfigurationOrPileNameToCards instanceof Map) {
		return setContext(SORT_PILES_KEY, new SortPilesState(sortPilesConfigurationOrPileNameToCards));
	}

	return setContext(
		SORT_PILES_KEY,
		SortPilesState.fromSortPilesConfiguration(sortPilesConfigurationOrPileNameToCards)
	);
}

export function getSortPilesState() {
	return getContext<ReturnType<typeof setSortPilesState>>(SORT_PILES_KEY);
}
